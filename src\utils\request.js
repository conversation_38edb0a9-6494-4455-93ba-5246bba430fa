import { logOut } from '@/utils/tool.js';
import { getEnv } from '@/utils/getEnv';

//全局配置
let baseHost = '';
let casHost = '';
let yltHost = '';
const { VITE_API_BASE_HOST, VITE_API_CAS_HOST, VITE_API_CAS_HOST__DEV, VITE_API_BASE_HOST__DEV } = getEnv();

if (import.meta.env.PROD) {
  // 生产环境，使用配置
  baseHost = VITE_API_BASE_HOST + '/api/api-v2';
  casHost = VITE_API_CAS_HOST;
  yltHost = VITE_API_BASE_HOST;
} else {
  // 开发环境，使用代理
  baseHost = VITE_API_BASE_HOST__DEV + '/api/api-v2';
  casHost = VITE_API_CAS_HOST__DEV;
  yltHost = VITE_API_BASE_HOST__DEV;
}

function request(method, url, params, option) {
  return new Promise((resolve, reject) => {
    // #ifdef MP-WEIXIN
    let cookie = uni.getStorageSync('ylb-cookie');
    // #endif
    uni.request({
      method: method,
      url,
      header: {
        // #ifdef MP-WEIXIN
        cookie: cookie,
        // #endif
        systemType: 'app',
        'system-Type': 'app',
        // "X-Trace-Id":
        // 	Math.random().toString(36).substring(2, 6) +
        // 	"-" +
        // 	new Date().getTime() // 本次请求唯一标识
      },
      data: params,
      // 请求成功
      success: ({ data }) => {
        if (option && option.ignoreStatusCode) {
          resolve(data);
          return;
        }

        if (data.code === 20000) {
          // 正常回调
          resolve(data);
          return;
        }
        // 异常统一拦截
        const errMsg = data.msg || data.message;
        if (errMsg)
          uni.showToast({
            title: errMsg,
            icon: 'none',
          });
        // 令牌失效
        if ([40001].includes(data.code)) {
          const { ck } = Tool.getRoute.params();
          if (ck) {
            sessionStorage.setItem('_ck', ck);
            logOut('redirect');
          } else {
            // 去登陆
            uni.showToast({
              title: data.msg || '您的登录已失效，请重新登录',
              icon: 'none',
              mask: true,
            });
            const timer = setTimeout(() => {
              clearTimeout(timer);
              logOut('redirect');
            }, 1000);
          }
        }
        // 服务器错误
        // if (data.code == 50000) {
        // 	// 跳转到 500 页面
        // 	Tool.goPage.replace("/pages/error/500")
        // }
        // 异常回调
        reject(data);
      },
      // 请求失败
      error: () => {
        // uni.showToast({
        // 	title: "请求失败！请检查网络配置！",
        // 	icon: "none"
        // })
        // Tool.goPage.replace("/pages/error/500")
      },
    });
  });
}

export default {
  get(url, params) {
    return new Promise((resolve, reject) => {
      let requestUrl = url;
      // 如果不带域名，会自动拼上默认接口域名
      if (requestUrl.indexOf('http') === -1) requestUrl = baseHost + url;
      request('GET', requestUrl, params)
        .then((res) => {
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  casGet(url, params) {
    return new Promise((resolve, reject) => {
      request('GET', casHost + url, params, { ignoreStatusCode: true })
        .then((res) => {
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  yltGet(url, params) {
    return new Promise((resolve, reject) => {
      request('GET', yltHost + url, params, { ignoreStatusCode: true })
        .then((res) => {
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  post(url, params) {
    return new Promise((resolve, reject) => {
      let requestUrl = url;
      // 如果不带域名，会自动拼上默认接口域名
      if (requestUrl.indexOf('http') === -1) requestUrl = baseHost + url;
      request('POST', requestUrl, params)
        .then((res) => {
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  casPost(url, params) {
    return new Promise((resolve, reject) => {
      request('POST', casHost + url, params, { ignoreStatusCode: true })
        .then((res) => {
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  yltPost(url, params) {
    return new Promise((resolve, reject) => {
      request('POST', yltHost + url, params, { ignoreStatusCode: true })
        .then((res) => {
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  put(url, params) {
    return new Promise((resolve, reject) => {
      let requestUrl = url;
      // 如果不带域名，会自动拼上默认接口域名
      if (requestUrl.indexOf('http') === -1) requestUrl = baseHost + url;
      request('PUT', requestUrl, params)
        .then((res) => {
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  casPut(url, params) {
    return new Promise((resolve, reject) => {
      request('PUT', casHost + url, params, { ignoreStatusCode: true })
        .then((res) => {
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  delete(url, params) {
    return new Promise((resolve, reject) => {
      let requestUrl = url;
      // 如果不带域名，会自动拼上默认接口域名
      if (requestUrl.indexOf('http') === -1) requestUrl = baseHost + url;
      request('DELETE', requestUrl, params)
        .then((res) => {
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  casDelete(url, params) {
    return new Promise((resolve, reject) => {
      let requestUrl = url;
      // 如果不带域名，会自动拼上默认接口域名
      if (requestUrl.indexOf('http') === -1) requestUrl = baseHost + url;
      request('DELETE', casHost + url, params, { ignoreStatusCode: true })
        .then((res) => {
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  yltDelete(url, params) {
    return new Promise((resolve, reject) => {
      let requestUrl = url;
      // 如果不带域名，会自动拼上默认接口域名
      if (requestUrl.indexOf('http') === -1) requestUrl = baseHost + url;
      request('DELETE', yltHost + url, params, { ignoreStatusCode: true })
        .then((res) => {
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
};
