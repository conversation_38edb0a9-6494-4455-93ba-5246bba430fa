<template>
  <view class="ticket-list-page">
    <view class="list" :style="{
      borderTopLeftRadius: cityListVisible || labelVisible ? '0' : '60rpx',
    }">
      <template v-if="state.ticketList.length !== 0">
        <template v-for="(item, index) in state.ticketList" :key="index">
          <!-- 权益卡 -->
          <view v-if="activeTab == 2">
            <y-travel-card @click="toBuy(item)" :ticket="item" />
          </view>
        </template>
        <y-loadmore :status="loadStatus"></y-loadmore>
      </template>
      <template v-else>
        <y-empty style="margin-top: 300rpx">暂无内容</y-empty>
      </template>
    </view>
  </view>
</template>

<script setup>
import request from "@/utils/request.js";

import { onLoad, onReachBottom } from "@dcloudio/uni-app";
import { onBeforeMount, reactive, ref, watch } from "vue";
import { getEnv } from "@/utils/getEnv";

const props = defineProps({});
const routerParams = reactive({});

onLoad((option) => {
  for (let key in option) {
    routerParams[key] = option[key];
  }
  if (routerParams.index && routerParams.index != 3) {
    activeTab.value = routerParams.index;
    state.title = state.tabList[routerParams.index];
  }
});

// tab 相关
const tabArr = [
  {
    key: 0,
    label: "景区门票",
  },
  {
    key: 1,
    label: "组合套票",
  },
];
// tab 回调
const onTabChange = async (e) => {
  console.log(e);
  activeTab.value = e;
  state.title = state.tabList[e];
  await getList(e, "init");

  // currentTabKey.value = e;
  // switch (e) {
  //   case '1':
  //     await getTicket();
  //     break;
  //   case '2':
  //     await getPackageTicket();
  //     break;
  //   case '3':
  //     await getTravelCard();
  //     break;
  //   default:
  //     break;
  // }
};

//选中的城市
const activeCity = ref({});

//输入框内容
const inputSearch = ref("");
//搜索参数
const search = reactive({
  keyWord: "", //关键字
  labelId: [], //标签 id
});
watch(
  () => activeCity,
  async (e) => {
    state.cityCode = e.value.cityCode;
    await getList(activeTab.value, "init");
  },
  { deep: true }
);

const loadStatus = ref("loadmore");
//页数
const page = reactive({
  current: 1,
  pageSize: 10,
});
//
const isAutonym = ref(false);
const state = reactive({
  ticketList: [],
  title: "列表页",
  cityCode: "",
  tabList: ["景区门票", "组合套票", "权益卡"],
});
//tabClick
const activeTab = ref(2);
const tabClick = async (index) => {
  activeTab.value = index;
  state.title = state.tabList[index];
  await getList(index, "init");
};
//搜索标签
const searchLabel = (val) => {
  console.log(val);
  search.labelId = [val.id]; //搜标签
  search.keyWord = ""; //清空关键字
  inputSearch.value = val.name; //显示在输入框
  tabClick(activeTab.value);
};
// 显示隐藏城市
const cityListVisible = ref(false);
// 显示隐藏搜索标签
const labelVisible = ref(false);
//输入框提示语
const inputPlaceholder = ref("请输入商品名称、标签");
//修改输入框提示语
watch(cityListVisible, (val) => {
  if (val) {
    inputPlaceholder.value = "请输入城市名称";
  } else {
    inputPlaceholder.value = "请输入商品名称、标签";
    search.value = "";
  }
});

//点击城市事件
const onShowCityList = () => {
  cityListVisible.value = !cityListVisible.value;
};
//聚焦搜索事件
const focusSearch = () => {
  //搜索景区
  if (!cityListVisible.value) {
    labelVisible.value = true;
  }
  //搜索城市
};

//页面跳转
const toBuy = (item) => {
  if (!isAutonym.value) {
    uni.showModal({
      title: "实名认证",
      content: "权益卡开通需要进行实名认证，是否立即前往认证？",
      confirmText: "前往认证",
      confirmColor: "#FF3B30",
      cancelText: "暂不认证",
      success: (res) => {
        if (res.confirm) {
          sessionStorage.setItem("activeTab", activeTab.value);
          sessionStorage.setItem("traveItem", JSON.stringify(item));
          Tool.goPage.push(`/pages/certification/certification`);
          // 执行删除操作
        } else if (res.cancel) {
          console.log("用户点击取消");
        }
      },
    });
  } else {
    if (activeTab.value == 0) {
      Tool.goPage.push(`/pages/scenic/scenic?scenicId=${item.scenicId}`);
    } else if (activeTab.value == 1) {
      Tool.goPage.push(`/pages/scenic/scenic?storeGoodsId=${item.storeGoodsId}`);
    } else if (activeTab.value == 2) {
      Tool.goPage.push(`/pages/travelCardDetail/travelCardDetail?rightId=${item.rightsId}&storeGoodsId=${item.storeGoodsId}`);
    }
  }
};

//获取景点门票列表
const getScenicList = async () => {
  let params = {
    addressCode: state.cityCode,
    // addressCode:"",
    current: page.current,
    pageSize: page.pageSize,
    // isTravelCardRights: (!!routerParams.rights || routerParams.index == 3),
    search: {
      isRecommend: false,
      ...search,
    },
    storeId: routerParams.storeId,
  };

  const { data } = await request.post(`/appScenic/scenicPageList`, params);
  return data.records ? data.records : [];
};
//获取组合套票列表
const getComposeGoodsList = async () => {
  //组合套票
  let params = {
    current: page.current,
    pageSize: page.pageSize,
    search: {
      isRecommend: false,
      ...search,
    },
    storeId: routerParams.storeId,
  };

  const { data } = await request.post(`/appTicket/appComposeGoodsList`, params);
  return data.data ? data.data : [];
};
//获取权益卡列表
const getTravelCardList = async () => {
  console.log(search);
  let params = {
    current: page.current,
    pageSize: page.pageSize,
    isRecommend: false,
    storeId: routerParams.storeId,
    goodsName: inputSearch.value || "",
  };

  const { data } = await request.get(`/appScenic/appTravelGoodsPageList`, params);

  return data.data
    ? data.data.map((e) => {
      const picUrl = e.goodsBaseInfo.picUrl ? getEnv().VITE_IMG_HOST + e.goodsBaseInfo.picUrl.toString().split(",")[0] : "";
      console.log(picUrl);
      return {
        ...e.travelCardUnitInfo,
        picUrl,
      };
    })
    : [];
};
const hasNextPage = ref(true);
const getList = async (index, type) => {
  try {
    if (!hasNextPage.value && type != "init") return;
    switch (type) {
      case "init":
        page.current = 1;
        break;
      case "next":
        page.current += 1;
        break;
    }
    uni.showLoading({
      title: "易旅宝",
      mask: true,
    });
    loadStatus.value = "loading";
    let list = [];
    if (index == 0) {
      //景点门票
      list = await getScenicList();
    }
    if (index == 1) {
      //组合套票
      list = await getComposeGoodsList();
    }
    if (index == 2) {
      //权益卡
      list = await getTravelCardList();
    }
    if (type === "init") {
      state.ticketList = list;
    } else if (type === "next") {
      if (list.length > 0) state.ticketList = [...state.ticketList, ...list];
    }
    hasNextPage.value = list.length >= page.pageSize;
    if (hasNextPage.value) {
      loadStatus.value = "loadmore";
    } else {
      loadStatus.value = "nomore";
    }

    uni.hideLoading();
  } catch (err) {
    console.log(err);
  }
};

//
const cityKey = ref("");
const onSearch = (type) => {
  if (labelVisible.value && type === "confirm") {
    search.keyWord = inputSearch.value; //关键字
    search.labelId = []; //清空标签
    //搜索景区
    getList(activeTab.value, "init");
    labelVisible.value = false;
  } else if (cityListVisible.value && type === "input") {
    //搜索城市
    console.log("搜索城市");
    cityKey.value = inputSearch.value;
  }
};

onMounted(async () => {
  const userData = await Tool.getUserInfo(true);
  if (userData.realNameInfo?.idNumber) {
    //已实名
    isAutonym.value = true;
  }
});

onBeforeMount(async () => {
  if (routerParams.rights || routerParams.index == 3) {
    //我的权益票 权益卡特权
    state.tabList = ["景区门票", "组合套票"];
  }
  tabClick(activeTab.value);
});

onReachBottom(() => {
  getList(activeTab.value, "next");
});
</script>

<style lang="scss" scoped>
.ticket-page {
  padding: 0 30rpx;
  background: linear-gradient(180deg, #bbd9ff 0%, rgba(217, 233, 255, 0) 100%);
}

.ticket-list-page {
  background-color: #fff;
  height: 100%;

  .search {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 131rpx;
    padding: 0rpx 40rpx;
    background-color: #7ed3da;
    border-bottom-right-radius: 60rpx;
    transition: 0.2s;

    &.straight {
      border-bottom-right-radius: 0rpx;
    }

    &::after {
      position: absolute;
      bottom: -60rpx;
      left: 0%;
      content: "";
      display: block;
      width: 60rpx;
      height: 60rpx;
      background-color: #7ed3da;
      z-index: 0;
    }

    .seach-input {
      flex: 1;
      display: flex;
      align-items: center;
      // width: 500rpx;
      height: 76rpx;
      padding: 0 23rpx;
      border-radius: 45rpx;
      background: #57c5ce;

      .input {
        color: #fff;
        caret-color: #fff;
      }

      .icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 50rpx;
        height: 50rpx;
      }

      .placeholder-style {
        font-size: 26rpx;
        color: #fff;
      }
    }

    .location {
      display: flex;
      align-items: center;
      max-width: 200rpx;
      font-size: 28rpx;
      margin-left: 20rpx;
      text-align: right;
      color: #fff;

      .text {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        flex: 1;
        // width: 0;
      }

      .icon {
        margin-left: 12rpx;
      }
    }
  }

  .list {
    position: relative;
    padding: 10rpx 40rpx;
    border-top-left-radius: 60rpx;
    background-color: #fff;
    transition: border-radius 0.5s;
  }
}
</style>
